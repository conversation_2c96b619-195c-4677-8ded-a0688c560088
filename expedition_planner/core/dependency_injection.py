"""
Dependency Injection Container for Expedition Planner.
Provides centralized service registration and resolution.
"""

import logging
from typing import Any, Dict, Type, TypeVar, Callable, Optional, Protocol
from abc import ABC, abstractmethod

from langchain_ollama import OllamaLLM

from ..config.expedition_config import ExpeditionConfig
from ..services.llm_enhancer import LLMDataEnhancer, LLMInterface
from ..services.data_validator import DataValidator
from ..services.text_processor import TextProcessor, TextExtractorInterface
from ..core.text_extractor import create_text_extractor
from ..utils.error_handlers import ConfigurationError

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceFactory(Protocol):
    """Protocol for service factory functions."""
    
    def __call__(self, container: 'DIContainer') -> Any:
        """Create a service instance."""
        ...


class DIContainer:
    """Dependency injection container for managing services."""
    
    def __init__(self):
        """Initialize the DI container."""
        self._services: Dict[Type, ServiceFactory] = {}
        self._singletons: Dict[Type, Any] = {}
        self._singleton_flags: Dict[Type, bool] = {}
        self._config: Optional[ExpeditionConfig] = None
    
    def register(
        self, 
        interface: Type[T], 
        factory: ServiceFactory, 
        singleton: bool = False
    ) -> 'DIContainer':
        """
        Register a service with the container.
        
        Args:
            interface: The interface/type to register
            factory: Factory function to create the service
            singleton: Whether to create only one instance
            
        Returns:
            Self for method chaining
        """
        self._services[interface] = factory
        self._singleton_flags[interface] = singleton
        
        logger.debug(f"Registered service: {interface.__name__} (singleton: {singleton})")
        return self
    
    def register_instance(self, interface: Type[T], instance: T) -> 'DIContainer':
        """
        Register a specific instance as a singleton.
        
        Args:
            interface: The interface/type to register
            instance: The instance to register
            
        Returns:
            Self for method chaining
        """
        self._singletons[interface] = instance
        self._singleton_flags[interface] = True
        
        logger.debug(f"Registered instance: {interface.__name__}")
        return self
    
    def get(self, interface: Type[T]) -> T:
        """
        Get a service instance from the container.
        
        Args:
            interface: The interface/type to resolve
            
        Returns:
            Service instance
            
        Raises:
            ConfigurationError: If service is not registered
        """
        # Check if we have a singleton instance
        if interface in self._singletons:
            return self._singletons[interface]
        
        # Check if service is registered
        if interface not in self._services:
            raise ConfigurationError(
                f"Service {interface.__name__} not registered",
                {"available_services": list(self._services.keys())}
            )
        
        # Create instance using factory
        factory = self._services[interface]
        instance = factory(self)
        
        # Store as singleton if configured
        if self._singleton_flags.get(interface, False):
            self._singletons[interface] = instance
        
        logger.debug(f"Created service instance: {interface.__name__}")
        return instance
    
    def has(self, interface: Type) -> bool:
        """
        Check if a service is registered.
        
        Args:
            interface: The interface/type to check
            
        Returns:
            True if registered, False otherwise
        """
        return interface in self._services or interface in self._singletons
    
    def clear_singletons(self) -> None:
        """Clear all singleton instances."""
        self._singletons.clear()
        logger.debug("Cleared all singleton instances")
    
    def get_config(self) -> ExpeditionConfig:
        """Get the configuration instance."""
        if self._config is None:
            self._config = ExpeditionConfig.load_from_env()
            self._config.validate()
        return self._config
    
    def set_config(self, config: ExpeditionConfig) -> None:
        """Set the configuration instance."""
        config.validate()
        self._config = config
        # Clear singletons that might depend on config
        self.clear_singletons()


class ServiceBuilder:
    """Builder for creating and configuring services."""
    
    def __init__(self, container: DIContainer):
        """Initialize the service builder."""
        self.container = container
    
    def build_llm(self) -> LLMInterface:
        """Build LLM service."""
        config = self.container.get_config()
        return OllamaLLM(
            model=config.llm.model,
            base_url=config.llm.base_url,
            temperature=config.llm.temperature,
            num_ctx=config.llm.num_ctx,
            num_predict=config.llm.num_predict,
            repeat_penalty=config.llm.repeat_penalty,
            top_k=config.llm.top_k,
            top_p=config.llm.top_p,
            request_timeout=config.llm.request_timeout,
            keep_alive=config.llm.keep_alive,
        )
    
    def build_text_extractor(self) -> TextExtractorInterface:
        """Build text extractor service."""
        return create_text_extractor()
    
    def build_llm_enhancer(self) -> LLMDataEnhancer:
        """Build LLM data enhancer service."""
        llm = self.container.get(LLMInterface)
        config = self.container.get_config()
        return LLMDataEnhancer(llm, config.llm)
    
    def build_data_validator(self) -> DataValidator:
        """Build data validator service."""
        config = self.container.get_config()
        return DataValidator(config.validation)
    
    def build_text_processor(self) -> TextProcessor:
        """Build text processor service."""
        text_extractor = self.container.get(TextExtractorInterface)
        config = self.container.get_config()
        return TextProcessor(text_extractor, config.processing)


def setup_container(config: Optional[ExpeditionConfig] = None) -> DIContainer:
    """
    Set up the dependency injection container with all services.
    
    Args:
        config: Optional configuration instance
        
    Returns:
        Configured DI container
    """
    container = DIContainer()
    
    # Set configuration
    if config:
        container.set_config(config)
    
    builder = ServiceBuilder(container)
    
    # Register core services
    container.register(
        ExpeditionConfig,
        lambda c: c.get_config(),
        singleton=True
    )
    
    # Register LLM service
    container.register(
        LLMInterface,
        lambda c: builder.build_llm(),
        singleton=True
    )
    
    # Register text extractor
    container.register(
        TextExtractorInterface,
        lambda c: builder.build_text_extractor(),
        singleton=True
    )
    
    # Register business services
    container.register(
        LLMDataEnhancer,
        lambda c: builder.build_llm_enhancer(),
        singleton=True
    )
    
    container.register(
        DataValidator,
        lambda c: builder.build_data_validator(),
        singleton=True
    )
    
    container.register(
        TextProcessor,
        lambda c: builder.build_text_processor(),
        singleton=True
    )
    
    logger.info("Dependency injection container configured successfully")
    return container


def create_test_container() -> DIContainer:
    """
    Create a container configured for testing with mock services.

    Returns:
        Test-configured DI container
    """
    container = DIContainer()

    # Use test configuration
    test_config = ExpeditionConfig.load_from_env()
    test_config.llm.model = "test-model"
    test_config.llm.base_url = "http://test:11434"
    container.set_config(test_config)

    # Register configuration service
    container.register(
        ExpeditionConfig,
        lambda c: c.get_config(),
        singleton=True
    )

    # Register mock services for testing
    class MockLLM:
        def invoke(self, prompt: str) -> str:
            return '{"date": "2024-01-01", "location": "Test Location"}'

    class MockTextExtractor:
        def extract_text(self, file_path: str) -> str:
            return "Test document content"

    container.register_instance(LLMInterface, MockLLM())
    container.register_instance(TextExtractorInterface, MockTextExtractor())

    # Register other services normally
    builder = ServiceBuilder(container)

    container.register(
        LLMDataEnhancer,
        lambda c: builder.build_llm_enhancer(),
        singleton=True
    )

    container.register(
        DataValidator,
        lambda c: builder.build_data_validator(),
        singleton=True
    )

    container.register(
        TextProcessor,
        lambda c: builder.build_text_processor(),
        singleton=True
    )

    logger.info("Test dependency injection container configured")
    return container


# Global container instance
_global_container: Optional[DIContainer] = None


def get_container() -> DIContainer:
    """
    Get the global container instance.
    
    Returns:
        Global DI container
    """
    global _global_container
    if _global_container is None:
        _global_container = setup_container()
    return _global_container


def set_container(container: DIContainer) -> None:
    """
    Set the global container instance.
    
    Args:
        container: Container to set as global
    """
    global _global_container
    _global_container = container


def reset_container() -> None:
    """Reset the global container."""
    global _global_container
    _global_container = None
