"""
LLM Data Enhancement Service for Expedition Planner.
Handles all LLM-based data enhancement operations.
"""

import json
import logging
import re
from typing import Dict, Any, Optional, Protocol
from datetime import datetime

from ..config.expedition_config import ExpeditionConfig, LLMConfig
from ..utils.error_handlers import LLMProcessingError, handle_expedition_errors

logger = logging.getLogger(__name__)


class LLMInterface(Protocol):
    """Protocol for LLM implementations."""
    
    def invoke(self, prompt: str) -> str:
        """Invoke the LLM with a prompt and return the response."""
        ...


class LLMDataEnhancer:
    """Service for enhancing extracted data using LLM."""
    
    def __init__(self, llm: LLMInterface, config: LLMConfig):
        """Initialize the LLM data enhancer."""
        self.llm = llm
        self.config = config
        self.enhancement_template = self._create_enhancement_template()
    
    def _create_enhancement_template(self) -> str:
        """Create the standard enhancement prompt template."""
        return """Extract comprehensive expedition data from this document text and enhance the existing data.

DOCUMENT TEXT:
{raw_text}

CURRENT EXTRACTED DATA:
{structured_data}

Instructions:
1. KEEP all data that has already been extracted
2. ADD any missing information from the document text
3. CORRECT any errors in the extracted data
4. Extract ALL time-based events for the schedule array
5. Extract ALL group information with colors, times, and activities
6. Extract equipment counts (zodiacs, twins)
7. Extract personnel information (guides, drivers)
8. Extract tide data with times and heights
9. Extract detailed operational notes
10. Return ONLY valid JSON with no additional text

REQUIRED JSON STRUCTURE:
{{
  "date": "YYYY-MM-DD",
  "location": "Location Name",
  "arrival_time": "HH:MM",
  "departure_time": "HH:MM",
  "operation_type": "am_only|pm_only|combined",
  "groups": [
    {{
      "groupName": "Color",
      "color": "Color",
      "departureTime": "HH:MM",
      "returnTime": "HH:MM",
      "activity": "Activity Description"
    }}
  ],
  "schedule": [
    {{
      "time": "HH:MM",
      "type": "arrival|departure|briefing|disembarkation|zodiac_drop|custom",
      "description": "Event description",
      "group": "Group Color (if applicable)"
    }}
  ],
  "tides": [
    {{
      "time": "HH:MM",
      "height": 2.0,
      "label": "Low Tide|High Tide"
    }}
  ],
  "equipment": {{
    "zodiacs": 8,
    "twins": 1,
    "other": []
  }},
  "personnel": {{
    "total_count": 0,
    "guides": [],
    "drivers": []
  }},
  "weather": "Weather description",
  "notes": "Operational notes and observations"
}}

Return ONLY the enhanced JSON structure with no additional text."""
    
    @handle_expedition_errors
    def enhance_data(self, raw_text: str, structured_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance structured data using LLM.
        
        Args:
            raw_text: Original document text
            structured_data: Previously extracted structured data
            
        Returns:
            Enhanced structured data
            
        Raises:
            LLMProcessingError: If LLM processing fails
        """
        try:
            # Validate inputs
            if not raw_text or len(raw_text.strip()) < 10:
                raise LLMProcessingError("Insufficient text content for enhancement")
            
            if not isinstance(structured_data, dict):
                raise LLMProcessingError("Structured data must be a dictionary")
            
            # Create enhancement prompt
            prompt = self.enhancement_template.format(
                raw_text=raw_text[:self.config.num_ctx // 2],  # Limit text to fit context
                structured_data=json.dumps(structured_data, indent=2)
            )
            
            # Get LLM response with retry logic
            response = self._invoke_llm_with_retry(prompt)
            
            # Parse and validate response
            enhanced_data = self._parse_llm_response(response)
            
            # Merge with original data
            merged_data = self._merge_data(structured_data, enhanced_data)
            
            logger.info("Successfully enhanced data with LLM")
            return merged_data
            
        except LLMProcessingError:
            raise
        except Exception as e:
            raise LLMProcessingError(f"Unexpected error during LLM enhancement: {e}")
    
    def _invoke_llm_with_retry(self, prompt: str) -> str:
        """Invoke LLM with retry logic."""
        last_error = None
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.llm.invoke(prompt)
                if response and len(response.strip()) > 10:
                    return response
                else:
                    raise LLMProcessingError("LLM returned empty or insufficient response")
                    
            except Exception as e:
                last_error = e
                logger.warning(f"LLM invocation attempt {attempt + 1} failed: {e}")
                
                if attempt < self.config.max_retries - 1:
                    # Wait before retry (exponential backoff)
                    import time
                    time.sleep(2 ** attempt)
        
        raise LLMProcessingError(f"LLM invocation failed after {self.config.max_retries} attempts: {last_error}")
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response and extract JSON."""
        try:
            # Clean up response
            response = response.strip()
            
            # Remove common prefixes
            prefixes_to_remove = [
                "ENHANCED EXTRACTED DATA:",
                "ENHANCED DATA:",
                "RESULT:",
                "JSON:",
                "```json",
                "```"
            ]
            
            for prefix in prefixes_to_remove:
                if response.startswith(prefix):
                    response = response.replace(prefix, "").strip()
            
            # Extract JSON using regex
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                raise LLMProcessingError("No valid JSON found in LLM response")
            
            json_str = json_match.group(0)
            
            # Parse JSON
            try:
                enhanced_data = json.loads(json_str)
                if not isinstance(enhanced_data, dict):
                    raise LLMProcessingError("LLM response is not a valid JSON object")
                
                return enhanced_data
                
            except json.JSONDecodeError as e:
                raise LLMProcessingError(f"Failed to parse JSON from LLM response: {e}")
                
        except LLMProcessingError:
            raise
        except Exception as e:
            raise LLMProcessingError(f"Error parsing LLM response: {e}")
    
    def _merge_data(self, original: Dict[str, Any], enhanced: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge original and enhanced data, prioritizing enhanced data for non-empty values.
        
        Args:
            original: Original structured data
            enhanced: Enhanced data from LLM
            
        Returns:
            Merged data dictionary
        """
        merged = original.copy()
        
        for key, value in enhanced.items():
            # Only use enhanced value if it's not empty/null
            if value is not None and value != "" and value != [] and value != {}:
                merged[key] = value
        
        return merged
    
    def validate_enhanced_data(self, data: Dict[str, Any]) -> bool:
        """
        Validate enhanced data structure.
        
        Args:
            data: Enhanced data to validate
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ["date", "location"]
        
        for field in required_fields:
            if field not in data or not data[field]:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate date format
        if data.get("date"):
            date_pattern = r"^\d{4}-\d{2}-\d{2}$"
            if not re.match(date_pattern, data["date"]):
                logger.warning(f"Invalid date format: {data['date']}")
                return False
        
        # Validate time formats
        time_pattern = r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
        time_fields = ["arrival_time", "departure_time"]
        
        for field in time_fields:
            if data.get(field) and not re.match(time_pattern, data[field]):
                logger.warning(f"Invalid time format for {field}: {data[field]}")
                return False
        
        return True
