"""
Text Processing Service for Expedition Planner.
Handles text extraction, preprocessing, and structured data extraction.
"""

import re
import logging
from typing import Dict, Any, List, Optional, Protocol
from datetime import datetime

from ..config.expedition_config import ProcessingConfig
from ..utils.error_handlers import DocumentExtractionError, handle_expedition_errors

logger = logging.getLogger(__name__)


class TextExtractorInterface(Protocol):
    """Protocol for text extraction implementations."""
    
    def extract_text(self, file_path: str) -> str:
        """Extract text from a file."""
        ...


class TextProcessor:
    """Service for processing and extracting structured data from text."""
    
    def __init__(self, text_extractor: TextExtractorInterface, config: ProcessingConfig):
        """Initialize the text processor."""
        self.text_extractor = text_extractor
        self.config = config
        self._compile_regex_patterns()
    
    def _compile_regex_patterns(self):
        """Pre-compile regex patterns for performance."""
        # Date patterns
        self.date_patterns = [
            re.compile(r'\b(\d{1,2})[/-](\d{1,2})[/-](\d{4})\b'),
            re.compile(r'\b(\d{4})[/-](\d{1,2})[/-](\d{1,2})\b'),
            re.compile(r'\b(\d{1,2})\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{4})\b', re.IGNORECASE),
        ]
        
        # Time patterns
        self.time_patterns = [
            re.compile(r'\b(\d{1,2}):(\d{2})\s*(AM|PM)?\b', re.IGNORECASE),
            re.compile(r'\b(\d{1,2})\.(\d{2})\s*(AM|PM)?\b', re.IGNORECASE),
            re.compile(r'\b(\d{1,2})\s*(AM|PM)\b', re.IGNORECASE),
        ]
        
        # Location patterns
        self.location_patterns = [
            re.compile(r'(?:location|site|place|destination):\s*([^\n\r]+)', re.IGNORECASE),
            re.compile(r'(?:at|in|near)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)', re.IGNORECASE),
        ]
        
        # Group/color patterns
        self.group_patterns = [
            re.compile(r'\b(red|blue|green|yellow|orange|purple|pink|white|black|brown)\s+group\b', re.IGNORECASE),
            re.compile(r'\bgroup\s+(red|blue|green|yellow|orange|purple|pink|white|black|brown)\b', re.IGNORECASE),
            re.compile(r'\b(red|blue|green|yellow|orange|purple|pink|white|black|brown)\s+team\b', re.IGNORECASE),
        ]
        
        # Tide patterns
        self.tide_patterns = [
            re.compile(r'(?:low|high)\s+tide\s+(?:at\s+)?(\d{1,2}):(\d{2})', re.IGNORECASE),
            re.compile(r'tide\s+(\d{1,2}):(\d{2})\s+(\d+\.?\d*)\s*m?', re.IGNORECASE),
        ]
        
        # Equipment patterns
        self.equipment_patterns = [
            re.compile(r'(\d+)\s+zodiac[s]?', re.IGNORECASE),
            re.compile(r'(\d+)\s+twin[s]?', re.IGNORECASE),
            re.compile(r'zodiac[s]?\s*:\s*(\d+)', re.IGNORECASE),
        ]
    
    @handle_expedition_errors
    def extract_text_from_files(self, file_paths: List[str]) -> List[str]:
        """
        Extract text from multiple files.
        
        Args:
            file_paths: List of file paths to process
            
        Returns:
            List of extracted text strings
            
        Raises:
            DocumentExtractionError: If extraction fails
        """
        try:
            extracted_texts = []
            failed_files = []
            
            for file_path in file_paths:
                try:
                    text = self.text_extractor.extract_text(file_path)
                    if text and len(text.strip()) > 10:
                        extracted_texts.append(text)
                        logger.info(f"Successfully extracted text from: {file_path}")
                    else:
                        failed_files.append(file_path)
                        logger.warning(f"No meaningful text extracted from: {file_path}")
                        
                except Exception as e:
                    failed_files.append(file_path)
                    logger.error(f"Failed to extract text from {file_path}: {e}")
            
            if not extracted_texts:
                raise DocumentExtractionError(
                    f"No text could be extracted from any of the {len(file_paths)} files",
                    {"failed_files": failed_files}
                )
            
            if failed_files:
                logger.warning(f"Failed to extract text from {len(failed_files)} files: {failed_files}")
            
            return extracted_texts
            
        except DocumentExtractionError:
            raise
        except Exception as e:
            raise DocumentExtractionError(f"Unexpected error during text extraction: {e}")
    
    @handle_expedition_errors
    def extract_structured_data(self, text: str, location: str = "") -> Dict[str, Any]:
        """
        Extract structured data from text using regex patterns.
        
        Args:
            text: Text to process
            location: Optional location context
            
        Returns:
            Dictionary containing structured data
            
        Raises:
            DocumentExtractionError: If extraction fails
        """
        try:
            if not text or len(text.strip()) < 10:
                raise DocumentExtractionError("Insufficient text content for extraction")
            
            # Initialize structured data
            structured_data = {
                "date": "",
                "location": location or "",
                "arrival_time": "",
                "departure_time": "",
                "operation_type": "combined",
                "groups": [],
                "schedule": [],
                "tides": [],
                "equipment": {"zodiacs": 0, "twins": 0, "other": []},
                "personnel": {"total_count": 0, "guides": [], "drivers": []},
                "weather": "",
                "notes": ""
            }
            
            # Extract each type of data
            structured_data["date"] = self._extract_date(text)
            if not structured_data["location"]:
                structured_data["location"] = self._extract_location(text)
            
            times = self._extract_times(text)
            if times:
                structured_data["arrival_time"] = times[0] if len(times) > 0 else ""
                structured_data["departure_time"] = times[-1] if len(times) > 1 else ""
            
            structured_data["groups"] = self._extract_groups(text)
            structured_data["schedule"] = self._extract_schedule_events(text)
            structured_data["tides"] = self._extract_tides(text)
            structured_data["equipment"] = self._extract_equipment(text)
            structured_data["weather"] = self._extract_weather(text)
            structured_data["notes"] = self._extract_notes(text)
            
            # Determine operation type
            structured_data["operation_type"] = self._determine_operation_type(structured_data)
            
            logger.info("Successfully extracted structured data from text")
            return structured_data
            
        except DocumentExtractionError:
            raise
        except Exception as e:
            raise DocumentExtractionError(f"Unexpected error during structured extraction: {e}")
    
    def _extract_date(self, text: str) -> str:
        """Extract date from text."""
        for pattern in self.date_patterns:
            match = pattern.search(text)
            if match:
                try:
                    groups = match.groups()
                    if len(groups) == 3:
                        # Try different date formats
                        if len(groups[2]) == 4:  # Year is last
                            if int(groups[0]) > 12:  # DD/MM/YYYY
                                return f"{groups[2]}-{groups[1]:0>2}-{groups[0]:0>2}"
                            else:  # MM/DD/YYYY
                                return f"{groups[2]}-{groups[0]:0>2}-{groups[1]:0>2}"
                        elif len(groups[0]) == 4:  # YYYY/MM/DD
                            return f"{groups[0]}-{groups[1]:0>2}-{groups[2]:0>2}"
                except (ValueError, IndexError):
                    continue
        
        # Default to current date if no date found
        return datetime.now().strftime("%Y-%m-%d")
    
    def _extract_times(self, text: str) -> List[str]:
        """Extract times from text."""
        times = []
        for pattern in self.time_patterns:
            matches = pattern.findall(text)
            for match in matches:
                try:
                    if isinstance(match, tuple):
                        if len(match) == 3:  # Hour, minute, AM/PM
                            hour, minute, ampm = match
                            hour = int(hour)
                            minute = int(minute) if minute else 0
                            
                            if ampm and ampm.upper() == 'PM' and hour < 12:
                                hour += 12
                            elif ampm and ampm.upper() == 'AM' and hour == 12:
                                hour = 0
                            
                            if 0 <= hour <= 23 and 0 <= minute <= 59:
                                times.append(f"{hour:02d}:{minute:02d}")
                        elif len(match) == 2:  # Hour, minute
                            hour, minute = match
                            hour = int(hour)
                            minute = int(minute)
                            
                            if 0 <= hour <= 23 and 0 <= minute <= 59:
                                times.append(f"{hour:02d}:{minute:02d}")
                except (ValueError, IndexError):
                    continue
        
        return sorted(list(set(times)))  # Remove duplicates and sort
    
    def _extract_location(self, text: str) -> str:
        """Extract location from text."""
        for pattern in self.location_patterns:
            match = pattern.search(text)
            if match:
                location = match.group(1).strip()
                if len(location) > 2:
                    return location
        
        return "Unknown Location"
    
    def _extract_groups(self, text: str) -> List[Dict[str, Any]]:
        """Extract group information from text."""
        groups = []
        found_colors = set()
        
        for pattern in self.group_patterns:
            matches = pattern.findall(text)
            for match in matches:
                color = match.lower() if isinstance(match, str) else match[0].lower()
                if color not in found_colors:
                    found_colors.add(color)
                    groups.append({
                        "groupName": color.title(),
                        "color": color.title(),
                        "departureTime": "",
                        "returnTime": "",
                        "activity": ""
                    })
        
        return groups
    
    def _extract_schedule_events(self, text: str) -> List[Dict[str, Any]]:
        """Extract schedule events from text."""
        events = []
        times = self._extract_times(text)
        
        # Create basic events from extracted times
        for i, time_str in enumerate(times):
            event_type = "custom"
            description = f"Event at {time_str}"
            
            # Try to determine event type from context
            if i == 0:
                event_type = "arrival"
                description = f"Arrival at {time_str}"
            elif i == len(times) - 1:
                event_type = "departure"
                description = f"Departure at {time_str}"
            
            events.append({
                "time": time_str,
                "type": event_type,
                "description": description,
                "group": ""
            })
        
        return events
    
    def _extract_tides(self, text: str) -> List[Dict[str, Any]]:
        """Extract tide information from text."""
        tides = []
        
        for pattern in self.tide_patterns:
            matches = pattern.findall(text)
            for match in matches:
                try:
                    if len(match) >= 2:
                        hour = int(match[0])
                        minute = int(match[1])
                        height = float(match[2]) if len(match) > 2 else 0.0
                        
                        if 0 <= hour <= 23 and 0 <= minute <= 59:
                            tides.append({
                                "time": f"{hour:02d}:{minute:02d}",
                                "height": height,
                                "label": "Tide"
                            })
                except (ValueError, IndexError):
                    continue
        
        return tides
    
    def _extract_equipment(self, text: str) -> Dict[str, Any]:
        """Extract equipment information from text."""
        equipment = {"zodiacs": 0, "twins": 0, "other": []}
        
        for pattern in self.equipment_patterns:
            matches = pattern.findall(text)
            for match in matches:
                try:
                    count = int(match)
                    pattern_str = pattern.pattern.lower()
                    
                    if "zodiac" in pattern_str:
                        equipment["zodiacs"] = max(equipment["zodiacs"], count)
                    elif "twin" in pattern_str:
                        equipment["twins"] = max(equipment["twins"], count)
                except (ValueError, IndexError):
                    continue
        
        return equipment
    
    def _extract_weather(self, text: str) -> str:
        """Extract weather information from text."""
        weather_keywords = ["weather", "conditions", "wind", "rain", "sunny", "cloudy", "temperature"]
        
        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in weather_keywords):
                # Extract the relevant part of the line
                weather_info = line.strip()
                if len(weather_info) > 10 and len(weather_info) < 200:
                    return weather_info
        
        return ""
    
    def _extract_notes(self, text: str) -> str:
        """Extract operational notes from text."""
        # Look for sections that might contain notes
        note_keywords = ["notes", "observations", "remarks", "comments", "important"]
        
        lines = text.split('\n')
        notes_lines = []
        
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in note_keywords):
                # Include this line and potentially the next few lines
                notes_lines.append(line.strip())
        
        if notes_lines:
            return ' '.join(notes_lines)
        
        # If no specific notes section, return a summary of the first few lines
        summary_lines = [line.strip() for line in lines[:3] if line.strip()]
        return ' '.join(summary_lines)[:200]  # Limit to 200 characters
    
    def _determine_operation_type(self, data: Dict[str, Any]) -> str:
        """Determine operation type based on extracted data."""
        arrival_time = data.get("arrival_time", "")
        departure_time = data.get("departure_time", "")
        
        if arrival_time and departure_time:
            try:
                arrival_hour = int(arrival_time.split(':')[0])
                departure_hour = int(departure_time.split(':')[0])
                
                if arrival_hour >= 12 and departure_hour >= 12:
                    return "pm_only"
                elif arrival_hour < 12 and departure_hour < 12:
                    return "am_only"
                else:
                    return "combined"
            except (ValueError, IndexError):
                pass
        
        return "combined"
