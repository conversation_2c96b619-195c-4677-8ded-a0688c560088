"""
Error handling utilities for the Expedition Planner.
"""

import logging
import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from functools import wraps

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Enhanced Custom Exception Hierarchy
class ExpeditionProcessingError(Exception):
    """Base exception for expedition processing."""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()


class DocumentExtractionError(ExpeditionProcessingError):
    """Raised when document extraction fails."""
    pass


class LLMProcessingError(ExpeditionProcessingError):
    """Raised when LLM processing fails."""
    pass


class ValidationError(ExpeditionProcessingError):
    """Raised when data validation fails."""
    pass


class SecurityError(ExpeditionProcessingError):
    """Raised when security validation fails."""
    pass


class ConfigurationError(ExpeditionProcessingError):
    """Raised when configuration is invalid."""
    pass


# Legacy ProcessingError for backward compatibility
class ProcessingError(Exception):
    """Custom exception for processing errors."""

    def __init__(
        self,
        message: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize processing error."""
        super().__init__(message)
        self.message = message
        self.severity = severity
        self.details = details or {}
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary."""
        return {
            "message": self.message,
            "severity": self.severity.value,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "type": self.__class__.__name__,
        }


# Standardized error handler decorator
def handle_expedition_errors(func):
    """Decorator for consistent error handling."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ExpeditionProcessingError as e:
            # Known errors - log and return structured response
            logger.error(f"Expedition processing error in {func.__name__}: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__,
                "function": func.__name__,
                "details": e.details,
                "timestamp": e.timestamp
            }
        except Exception as e:
            # Unexpected errors - log with full traceback
            logger.exception(f"Unexpected error in {func.__name__}: {e}")
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_type": "UnexpectedError",
                "function": func.__name__,
                "details": {"original_error": str(e)},
                "timestamp": datetime.now().isoformat()
            }
    return wrapper


class ErrorHandler:
    """Centralized error handling and logging."""

    def __init__(self):
        """Initialize the error handler."""
        self.logger = logging.getLogger(__name__)
        self.error_history: List[Dict[str, Any]] = []

    def handle_document_error(
        self, file_path: str, error: Exception, context: Optional[str] = None
    ) -> ProcessingError:
        """Handle document processing errors."""
        try:
            error_msg = f"Document processing failed for {file_path}: {error!s}"

            # Determine severity based on error type
            if isinstance(error, FileNotFoundError):
                severity = ErrorSeverity.HIGH
            elif isinstance(error, PermissionError):
                severity = ErrorSeverity.HIGH
            elif isinstance(error, MemoryError):
                severity = ErrorSeverity.CRITICAL
            else:
                severity = ErrorSeverity.MEDIUM

            details = {
                "file_path": file_path,
                "error_type": type(error).__name__,
                "context": context,
                "traceback": traceback.format_exc(),
            }

            processing_error = ProcessingError(error_msg, severity, details)
            self._log_error(processing_error)

            return processing_error

        except Exception as e:
            # Fallback error handling
            fallback_error = ProcessingError(
                f"Error handler failed: {e!s}", ErrorSeverity.CRITICAL
            )
            self.logger.critical(f"Error handler failure: {e}")
            return fallback_error

    def handle_extraction_error(
        self, content: str, error: Exception, extraction_type: str = "general"
    ) -> ProcessingError:
        """Handle data extraction errors."""
        try:
            error_msg = f"Data extraction failed ({extraction_type}): {error!s}"

            # Determine severity
            if "timeout" in str(error).lower():
                severity = ErrorSeverity.HIGH
            elif "memory" in str(error).lower():
                severity = ErrorSeverity.CRITICAL
            else:
                severity = ErrorSeverity.MEDIUM

            details = {
                "extraction_type": extraction_type,
                "content_length": len(content),
                "content_preview": content[:200] + "..."
                if len(content) > 200
                else content,
                "error_type": type(error).__name__,
                "traceback": traceback.format_exc(),
            }

            processing_error = ProcessingError(error_msg, severity, details)
            self._log_error(processing_error)

            return processing_error

        except Exception as e:
            fallback_error = ProcessingError(
                f"Extraction error handler failed: {e!s}", ErrorSeverity.CRITICAL
            )
            self.logger.critical(f"Extraction error handler failure: {e}")
            return fallback_error

    def handle_analysis_error(
        self, data: Any, error: Exception, analysis_type: str = "statistical"
    ) -> ProcessingError:
        """Handle analysis errors."""
        try:
            error_msg = f"Analysis failed ({analysis_type}): {error!s}"

            # Determine severity
            if isinstance(error, ValueError):
                severity = ErrorSeverity.MEDIUM
            elif isinstance(error, TypeError):
                severity = ErrorSeverity.HIGH
            else:
                severity = ErrorSeverity.MEDIUM

            details = {
                "analysis_type": analysis_type,
                "data_type": type(data).__name__,
                "error_type": type(error).__name__,
                "traceback": traceback.format_exc(),
            }

            processing_error = ProcessingError(error_msg, severity, details)
            self._log_error(processing_error)

            return processing_error

        except Exception as e:
            fallback_error = ProcessingError(
                f"Analysis error handler failed: {e!s}", ErrorSeverity.CRITICAL
            )
            self.logger.critical(f"Analysis error handler failure: {e}")
            return fallback_error

    def handle_generation_error(
        self, format_type: str, error: Exception, data: Optional[Any] = None
    ) -> ProcessingError:
        """Handle report generation errors."""
        try:
            error_msg = f"Report generation failed ({format_type}): {error!s}"

            # Determine severity
            if "permission" in str(error).lower():
                severity = ErrorSeverity.HIGH
            elif "disk" in str(error).lower() or "space" in str(error).lower():
                severity = ErrorSeverity.CRITICAL
            else:
                severity = ErrorSeverity.MEDIUM

            details = {
                "format_type": format_type,
                "error_type": type(error).__name__,
                "has_data": data is not None,
                "traceback": traceback.format_exc(),
            }

            processing_error = ProcessingError(error_msg, severity, details)
            self._log_error(processing_error)

            return processing_error

        except Exception as e:
            fallback_error = ProcessingError(
                f"Generation error handler failed: {e!s}", ErrorSeverity.CRITICAL
            )
            self.logger.critical(f"Generation error handler failure: {e}")
            return fallback_error

    def handle_validation_error(
        self, validation_errors: List[str], context: str = "general"
    ) -> ProcessingError:
        """Handle validation errors."""
        try:
            error_msg = (
                f"Validation failed ({context}): {len(validation_errors)} errors found"
            )

            # Determine severity based on number of errors
            if len(validation_errors) > 10:
                severity = ErrorSeverity.HIGH
            elif len(validation_errors) > 5:
                severity = ErrorSeverity.MEDIUM
            else:
                severity = ErrorSeverity.LOW

            details = {
                "context": context,
                "error_count": len(validation_errors),
                "errors": validation_errors[:10],  # Limit to first 10 errors
                "truncated": len(validation_errors) > 10,
            }

            processing_error = ProcessingError(error_msg, severity, details)
            self._log_error(processing_error)

            return processing_error

        except Exception as e:
            fallback_error = ProcessingError(
                f"Validation error handler failed: {e!s}", ErrorSeverity.CRITICAL
            )
            self.logger.critical(f"Validation error handler failure: {e}")
            return fallback_error

    def _log_error(self, error: ProcessingError):
        """Log error with appropriate level."""
        error_dict = error.to_dict()
        self.error_history.append(error_dict)

        # Keep only last 100 errors in memory
        if len(self.error_history) > 100:
            self.error_history = self.error_history[-100:]

        # Log with appropriate level
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"{error.message} | Details: {error.details}")
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(f"{error.message} | Details: {error.details}")
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"{error.message} | Details: {error.details}")
        else:
            self.logger.info(f"{error.message} | Details: {error.details}")

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors."""
        if not self.error_history:
            return {"total_errors": 0, "by_severity": {}, "recent_errors": []}

        # Count by severity
        severity_counts = {}
        for error in self.error_history:
            severity = error["severity"]
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        # Get recent errors (last 10)
        recent_errors = self.error_history[-10:]

        return {
            "total_errors": len(self.error_history),
            "by_severity": severity_counts,
            "recent_errors": recent_errors,
            "last_error_time": self.error_history[-1]["timestamp"]
            if self.error_history
            else None,
        }

    def clear_error_history(self):
        """Clear error history."""
        self.error_history.clear()
        self.logger.info("Error history cleared")

    def create_user_friendly_message(self, error: ProcessingError) -> str:
        """Create user-friendly error message."""
        base_messages = {
            ErrorSeverity.LOW: "A minor issue occurred, but processing can continue.",
            ErrorSeverity.MEDIUM: "An error occurred that may affect results quality.",
            ErrorSeverity.HIGH: "A significant error occurred that prevents normal processing.",
            ErrorSeverity.CRITICAL: "A critical error occurred that stops all processing.",
        }

        base_message = base_messages.get(error.severity, "An unknown error occurred.")

        # Add specific guidance based on error details
        if "file" in error.message.lower():
            guidance = (
                " Please check that all files are accessible and in supported formats."
            )
        elif "memory" in error.message.lower():
            guidance = " Try processing fewer documents at once or use smaller files."
        elif "timeout" in error.message.lower():
            guidance = " The operation took too long. Try again or contact support."
        elif "permission" in error.message.lower():
            guidance = " Check file permissions and available disk space."
        else:
            guidance = " Please try again or contact support if the problem persists."

        return base_message + guidance
