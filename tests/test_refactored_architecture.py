"""
Test suite for the refactored architecture and code quality improvements.
"""

import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch
from datetime import datetime

from expedition_planner.config.expedition_config import ExpeditionConfig
from expedition_planner.core.dependency_injection import DIContainer, setup_container, create_test_container
from expedition_planner.services.llm_enhancer import LLMDataEnhancer
from expedition_planner.services.data_validator import DataValidator
from expedition_planner.services.text_processor import TextProcessor
from expedition_planner.agents.data_extractor import DataExtractionAgent
from expedition_planner.utils.error_handlers import (
    ExpeditionProcessingError,
    DocumentExtractionError,
    LLMProcessingError,
    ValidationError,
    handle_expedition_errors
)


class TestConfigurationManagement:
    """Test the new configuration management system."""
    
    def test_config_creation_from_env(self):
        """Test configuration creation from environment variables."""
        config = ExpeditionConfig.load_from_env()
        
        assert config.llm.model == "mistral-7b-v0-1-gguf:latest"
        assert config.llm.base_url == "http://127.0.0.1:11434"
        assert config.llm.temperature == 0.2
        assert config.processing.max_iterations == 2
        assert config.security.max_file_size_mb == 50
    
    def test_config_validation(self):
        """Test configuration validation."""
        config = ExpeditionConfig.load_from_env()
        
        # Valid config should pass
        assert config.validate() is True
        
        # Invalid config should raise error
        config.llm.temperature = -1.0  # Invalid temperature
        with pytest.raises(ValueError):
            config.validate()
    
    def test_config_to_dict(self):
        """Test configuration serialization."""
        config = ExpeditionConfig.load_from_env()
        config_dict = config.to_dict()
        
        assert "llm" in config_dict
        assert "processing" in config_dict
        assert "security" in config_dict
        assert "validation" in config_dict
        assert "output" in config_dict


class TestDependencyInjection:
    """Test the dependency injection container."""
    
    def test_container_setup(self):
        """Test container setup with all services."""
        container = setup_container()
        
        # Test that all services are registered
        assert container.has(ExpeditionConfig)
        assert container.has(LLMDataEnhancer)
        assert container.has(DataValidator)
        assert container.has(TextProcessor)
    
    def test_service_resolution(self):
        """Test service resolution from container."""
        container = create_test_container()
        
        # Test getting services
        config = container.get(ExpeditionConfig)
        validator = container.get(DataValidator)
        enhancer = container.get(LLMDataEnhancer)
        processor = container.get(TextProcessor)
        
        assert isinstance(config, ExpeditionConfig)
        assert isinstance(validator, DataValidator)
        assert isinstance(enhancer, LLMDataEnhancer)
        assert isinstance(processor, TextProcessor)
    
    def test_singleton_behavior(self):
        """Test singleton service behavior."""
        container = create_test_container()
        
        # Get the same service twice
        config1 = container.get(ExpeditionConfig)
        config2 = container.get(ExpeditionConfig)
        
        # Should be the same instance
        assert config1 is config2


class TestCustomExceptions:
    """Test the custom exception hierarchy."""
    
    def test_exception_hierarchy(self):
        """Test exception inheritance."""
        doc_error = DocumentExtractionError("Test error")
        llm_error = LLMProcessingError("Test error")
        val_error = ValidationError("Test error")
        
        assert isinstance(doc_error, ExpeditionProcessingError)
        assert isinstance(llm_error, ExpeditionProcessingError)
        assert isinstance(val_error, ExpeditionProcessingError)
    
    def test_exception_details(self):
        """Test exception details storage."""
        details = {"file": "test.pdf", "line": 42}
        error = DocumentExtractionError("Test error", details)
        
        assert error.message == "Test error"
        assert error.details == details
        assert error.timestamp is not None
    
    def test_error_handler_decorator(self):
        """Test the error handler decorator."""
        
        @handle_expedition_errors
        def test_function_success():
            return {"success": True, "data": "test"}
        
        @handle_expedition_errors
        def test_function_known_error():
            raise DocumentExtractionError("Known error")
        
        @handle_expedition_errors
        def test_function_unknown_error():
            raise ValueError("Unknown error")
        
        # Test successful execution
        result = test_function_success()
        assert result["success"] is True
        
        # Test known error handling
        result = test_function_known_error()
        assert result["success"] is False
        assert result["error_type"] == "DocumentExtractionError"
        
        # Test unknown error handling
        result = test_function_unknown_error()
        assert result["success"] is False
        assert result["error_type"] == "UnexpectedError"


class TestDataValidator:
    """Test the data validator service."""
    
    def setup_method(self):
        """Set up test fixtures."""
        config = ExpeditionConfig.load_from_env()
        self.validator = DataValidator(config.validation)
    
    def test_validate_complete_data(self):
        """Test validation of complete, valid data."""
        data = {
            "date": "2024-01-15",
            "location": "Test Location",
            "arrival_time": "08:00",
            "departure_time": "17:00",
            "operation_type": "combined",
            "groups": [
                {
                    "groupName": "Red",
                    "color": "Red",
                    "departureTime": "09:00",
                    "returnTime": "16:00",
                    "activity": "Hiking"
                }
            ],
            "schedule": [
                {
                    "time": "08:00",
                    "type": "arrival",
                    "description": "Ship arrival"
                }
            ],
            "tides": [
                {
                    "time": "12:00",
                    "height": 2.5,
                    "label": "High Tide"
                }
            ],
            "equipment": {
                "zodiacs": 8,
                "twins": 1,
                "other": []
            },
            "personnel": {
                "total_count": 15,
                "guides": ["Guide 1", "Guide 2"],
                "drivers": ["Driver 1"]
            },
            "weather": "Sunny, 15°C",
            "notes": "Test operation"
        }
        
        result = self.validator.validate_and_clean(data)
        assert result["success"] is True
        assert result["date"] == "2024-01-15"
        assert len(result["groups"]) == 1
    
    def test_validate_missing_required_fields(self):
        """Test validation with missing required fields."""
        data = {
            "location": "Test Location"
            # Missing date and activities
        }
        
        result = self.validator.validate_and_clean(data)
        assert result["success"] is True
        assert "date" in result  # Should be filled with default
        assert "activities" in result  # Should be filled with default
    
    def test_fix_invalid_time_formats(self):
        """Test fixing invalid time formats."""
        data = {
            "date": "2024-01-15",
            "location": "Test Location",
            "arrival_time": "8:30 AM",  # Invalid format
            "departure_time": "5:00 PM",  # Invalid format
            "activities": []
        }
        
        result = self.validator.validate_and_clean(data)
        assert result["success"] is True
        assert result["arrival_time"] == "08:30"
        assert result["departure_time"] == "17:00"


class TestLLMEnhancer:
    """Test the LLM enhancer service."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Create mock LLM
        self.mock_llm = Mock()
        self.mock_llm.invoke.return_value = json.dumps({
            "date": "2024-01-15",
            "location": "Enhanced Location",
            "groups": [{"groupName": "Blue", "color": "Blue"}]
        })
        
        config = ExpeditionConfig.load_from_env()
        self.enhancer = LLMDataEnhancer(self.mock_llm, config.llm)
    
    def test_enhance_data_success(self):
        """Test successful data enhancement."""
        raw_text = "Test document content with expedition details"
        structured_data = {
            "date": "2024-01-15",
            "location": "Original Location",
            "groups": []
        }
        
        result = self.enhancer.enhance_data(raw_text, structured_data)
        
        assert result["success"] is True
        assert result["location"] == "Enhanced Location"  # Should be enhanced
        assert len(result["groups"]) == 1  # Should be added
        assert self.mock_llm.invoke.called
    
    def test_enhance_data_llm_failure(self):
        """Test enhancement when LLM fails."""
        self.mock_llm.invoke.side_effect = Exception("LLM failed")
        
        raw_text = "Test document content"
        structured_data = {"date": "2024-01-15", "location": "Original"}
        
        result = self.enhancer.enhance_data(raw_text, structured_data)
        
        # Should handle error gracefully
        assert result["success"] is False
        assert result["error_type"] == "LLMProcessingError"


class TestRefactoredDataExtractionAgent:
    """Test the refactored DataExtractionAgent."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.container = create_test_container()
        self.agent = DataExtractionAgent(container=self.container)
    
    def test_agent_initialization_with_di(self):
        """Test agent initialization with dependency injection."""
        assert self.agent.container is not None
        assert self.agent.config is not None
        assert self.agent.llm_enhancer is not None
        assert self.agent.data_validator is not None
        assert self.agent.text_processor is not None
    
    def test_extract_from_documents_success(self):
        """Test successful document extraction with new architecture."""
        # Create temporary test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Test expedition document\nDate: 2024-01-15\nLocation: Test Site")
            test_file = f.name
        
        try:
            result = self.agent.extract_from_documents([test_file], "Test Location")
            
            assert result["success"] is True
            assert "extracted_data" in result
            assert "processed_files" in result
            assert result["location"] == "Test Location"
            
        finally:
            os.unlink(test_file)
    
    def test_extract_from_documents_no_files(self):
        """Test extraction with no valid files."""
        result = self.agent.extract_from_documents([], "Test Location")
        
        assert result["success"] is False
        assert result["error_type"] == "DocumentExtractionError"
    
    def test_method_decomposition(self):
        """Test that large methods have been broken down."""
        # Verify that the new smaller methods exist
        assert hasattr(self.agent, '_extract_text_from_files')
        assert hasattr(self.agent, '_process_text_to_structure')
        assert hasattr(self.agent, '_enhance_data_with_llm')
        assert hasattr(self.agent, '_validate_and_clean_data')
        assert hasattr(self.agent, '_create_success_result')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
